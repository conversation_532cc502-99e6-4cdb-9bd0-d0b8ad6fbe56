from .enemy import Enemy
import pygame
import math
import random


class TimeLord<PERSON><PERSON>(Enemy):
    """Ultra powerful boss that manipulates time and space"""

    def __init__(self, path, wave_number=1):
        super().__init__(path, wave_number)
        self.health = 100  # Reduced for faster, more tactical fights
        self.max_health = 100
        self.speed = 0.8
        self.reward = 700  # NERFED: was 800
        self.color = (75, 0, 130)  # Indigo
        self.size = 30
        self.damage = 4  # TimeLord Boss deals 4 damage to player

        # Phase Shift ability - dodges projectiles
        self.phase_shift_active = False
        self.phase_shift_timer = 0
        self.phase_shift_duration = 90  # 1.5 seconds
        self.phase_shift_cooldown = 480  # 8 seconds
        self.phase_shift_chance = 0.4  # 40% chance to dodge when active

        # Projectile Deflection ability - reflects projectiles back
        self.deflection_timer = 0
        self.deflection_cooldown = 600  # 10 seconds
        self.deflection_duration = 60   # 1 second
        self.deflection_active = False
        self.deflection_radius = 100

        # Enemy Summoning ability - spawns minions
        self.summon_timer = 0
        self.summon_cooldown = 720  # 12 seconds
        self.summoned_enemies = []  # Track summoned enemies

        # Boss resistances
        self.damage_reduction = 0.25  # Takes 75% damage
        self.adaptive_immunity_timer = 0  # Becomes immune to tower types that damage it most
        self.immune_tower_types = []  # Currently immune tower types (ordered list for FIFO)
        self.damage_by_tower_type = {}  # Track damage by tower type

        # Phase system
        self.phase = 1
        self.max_phases = 4

        # Status effect resistances but not immunities
        self.freeze_resistance = 0.5  # Takes half freeze duration
        self.wet_resistance = 0.3  # Takes 30% less wet duration

        # Visual effects
        self.phase_particles = []
        self.deflection_particles = []
        self.aura_rotation = 0
        self.pulse_timer = 0

        # Summoning flag for game loop
        self.should_summon_enemies = 0

    def update(self):
        """Update with time manipulation abilities"""
        # Update phase based on health
        health_percentage = self.health / self.max_health
        if health_percentage > 0.75:
            self.phase = 1
        elif health_percentage > 0.5:
            self.phase = 2
        elif health_percentage > 0.25:
            self.phase = 3
        else:
            self.phase = 4  # Final phase - most dangerous

        # Phase-based speed and abilities
        self.speed = 0.8 + (self.phase - 1) * 0.15

        super().update()

        # Update ability timers
        self.phase_shift_timer += 1
        self.deflection_timer += 1
        self.summon_timer += 1
        self.aura_rotation += 2
        self.pulse_timer += 0.1

        # Update adaptive immunity (phase 3+)
        if self.phase >= 3:
            self.adaptive_immunity_timer += 1
            self.update_adaptive_immunity()

        # Trigger abilities based on phase and timers
        self.update_boss_abilities()

        # Update visual effects
        self.update_phase_particles()
        self.update_deflection_particles()

    def update_with_speed(self, speed_multiplier: float):
        """Update with speed multiplier for performance optimization"""
        # Update phase based on health
        health_percentage = self.health / self.max_health
        if health_percentage > 0.75:
            self.phase = 1
        elif health_percentage > 0.5:
            self.phase = 2
        elif health_percentage > 0.25:
            self.phase = 3
        else:
            self.phase = 4  # Final phase - most dangerous

        # Phase-based speed and abilities
        self.speed = 0.8 + (self.phase - 1) * 0.15

        super().update_with_speed(speed_multiplier)

        # Update ability timers with speed multiplier
        self.phase_shift_timer += speed_multiplier
        self.deflection_timer += speed_multiplier
        self.summon_timer += speed_multiplier
        self.aura_rotation += 2 * speed_multiplier
        self.pulse_timer += 0.1 * speed_multiplier

        # Update adaptive immunity (phase 3+)
        if self.phase >= 3:
            self.adaptive_immunity_timer += speed_multiplier
            self.update_adaptive_immunity()

        # Trigger abilities based on phase and timers
        self.update_boss_abilities()

        # Update visual effects
        self.update_phase_particles()
        self.update_deflection_particles()

    def update_boss_abilities(self):
        """Update boss abilities based on phase"""
        # Phase Shift ability (all phases)
        if (self.phase_shift_timer >= self.phase_shift_cooldown and
                not self.phase_shift_active):
            self.activate_phase_shift()

        if self.phase_shift_active:
            if self.phase_shift_timer >= self.phase_shift_cooldown + self.phase_shift_duration:
                self.deactivate_phase_shift()

        # Projectile Deflection ability (phase 2+)
        if (self.phase >= 2 and self.deflection_timer >= self.deflection_cooldown and
                not self.deflection_active):
            self.activate_deflection()

        if self.deflection_active:
            if self.deflection_timer >= self.deflection_duration:
                self.deactivate_deflection()

        # Enemy Summoning ability (phase 3+)
        if (self.phase >= 3 and self.summon_timer >= self.summon_cooldown):
            self.summon_enemies()

    def activate_phase_shift(self):
        """Activate phase shift - chance to dodge projectiles"""
        self.phase_shift_active = True
        self.phase_shift_timer = 0

        # Create phase shift particles
        for _ in range(15):
            particle = {
                'x': self.x + random.uniform(-20, 20),
                'y': self.y + random.uniform(-20, 20),
                'vx': random.uniform(-2, 2),
                'vy': random.uniform(-2, 2),
                'life': 45,
                'max_life': 45,
                'color': (255, 255, 255)
            }
            self.phase_particles.append(particle)

    def deactivate_phase_shift(self):
        """End phase shift effect"""
        self.phase_shift_active = False
        self.phase_shift_timer = 0

    def activate_deflection(self):
        """Activate projectile deflection field"""
        self.deflection_active = True
        self.deflection_timer = 0

        # Create deflection particles
        for _ in range(25):
            angle = random.uniform(0, 2 * math.pi)
            distance = random.uniform(30, self.deflection_radius)
            particle = {
                'x': self.x + math.cos(angle) * distance,
                'y': self.y + math.sin(angle) * distance,
                'angle': angle,
                'distance': distance,
                'life': 60,
                'max_life': 60,
                'color': (255, 215, 0)  # Gold
            }
            self.deflection_particles.append(particle)

    def deactivate_deflection(self):
        """End deflection effect"""
        self.deflection_active = False
        # Don't reset timer - let it continue for cooldown

    def summon_enemies(self):
        """Summon minion enemies based on phase"""
        self.summon_timer = 0

        # Number of enemies to summon based on phase
        summon_count = min(self.phase, 3)

        # This will be handled by the game loop to actually spawn enemies
        # We just mark that summoning should happen
        self.should_summon_enemies = summon_count

    def update_adaptive_immunity(self):
        """Update adaptive immunity system - becomes immune to most damaging tower types"""
        if self.adaptive_immunity_timer % 300 == 0:  # Every 5 seconds
            # Find the tower type that has dealt the most damage
            if self.damage_by_tower_type:
                most_damaging_type = max(self.damage_by_tower_type.items(), key=lambda x: x[1])[0]

                # Only add immunity if not already immune and if it actually dealt damage
                if most_damaging_type not in self.immune_tower_types and self.damage_by_tower_type[most_damaging_type] > 0:
                    self.immune_tower_types.append(most_damaging_type)

                # Limit to 2 immunities max to prevent complete immunity
                if len(self.immune_tower_types) > 2:
                    # Remove oldest immunity (first in list)
                    self.immune_tower_types.pop(0)

    def update_phase_particles(self):
        """Update phase shift particles"""
        for particle in self.phase_particles[:]:
            particle['x'] += particle['vx']
            particle['y'] += particle['vy']
            particle['life'] -= 1

            if particle['life'] <= 0:
                self.phase_particles.remove(particle)

    def update_deflection_particles(self):
        """Update deflection field particles"""
        for particle in self.deflection_particles[:]:
            # Rotate particles around boss
            particle['angle'] += 0.05
            particle['x'] = self.x + math.cos(particle['angle']) * particle['distance']
            particle['y'] = self.y + math.sin(particle['angle']) * particle['distance']
            particle['life'] -= 1

            if particle['life'] <= 0:
                self.deflection_particles.remove(particle)

    def can_dodge_projectile(self):
        """Check if boss can dodge incoming projectile"""
        if self.phase_shift_active:
            return random.random() < self.phase_shift_chance
        return False

    def should_deflect_projectile(self, projectile_x, projectile_y):
        """Check if projectile should be deflected"""
        if not self.deflection_active:
            return False

        distance = math.sqrt((projectile_x - self.x)**2 + (projectile_y - self.y)**2)
        return distance <= self.deflection_radius

    def take_damage(self, damage, tower_type: str = 'basic'):
        """Take damage with boss resistances and adaptive immunity"""
        # Check adaptive immunity (phase 3+)
        if self.phase >= 3 and tower_type in self.immune_tower_types:
            return 0

        # Phase shift dodge chance
        if self.can_dodge_projectile():
            return 0

        # Apply 3x damage multiplier for frozen enemies
        if self.frozen:
            damage = int(damage * 3.0)

        # Apply damage reduction
        reduced_damage = damage * (1 - self.damage_reduction)
        actual_damage = min(reduced_damage, self.health)

        # Track damage by tower type for adaptive immunity (only if actual damage > 0)
        if actual_damage > 0:
            if tower_type not in self.damage_by_tower_type:
                self.damage_by_tower_type[tower_type] = 0
            self.damage_by_tower_type[tower_type] += actual_damage

        self.health -= reduced_damage
        return actual_damage

    def check_projectile_deflection(self, projectile):
        """Check if a projectile should be deflected and handle deflection"""
        if not self.deflection_active:
            return {'deflected': False}

        # Check if projectile has already been deflected
        if hasattr(projectile, 'deflected_by_timelord') and projectile.deflected_by_timelord:
            return {'deflected': False}

        # Check if projectile is in deflection range
        distance = math.sqrt((projectile.x - self.x)**2 + (projectile.y - self.y)**2)
        if distance <= self.deflection_radius:
            # Mark projectile as deflected to prevent multiple deflections
            projectile.deflected_by_timelord = True

            # Handle different projectile velocity attribute names
            if hasattr(projectile, 'velocity_x') and hasattr(projectile, 'velocity_y'):
                # Standard projectiles (BasicProjectile, HomingProjectile, etc.)
                vel_x, vel_y = projectile.velocity_x, projectile.velocity_y

                # Reverse direction with random angle offset
                import random
                angle_offset = random.uniform(-0.5, 0.5)
                speed = math.sqrt(vel_x**2 + vel_y**2)
                current_angle = math.atan2(vel_y, vel_x)
                new_angle = current_angle + math.pi + angle_offset  # Add pi for 180 degree flip

                projectile.velocity_x = math.cos(new_angle) * speed
                projectile.velocity_y = math.sin(new_angle) * speed

            elif hasattr(projectile, 'dx') and hasattr(projectile, 'dy'):
                # HomingMissile and similar projectiles
                vel_x, vel_y = projectile.dx, projectile.dy

                # Reverse direction with random angle offset
                import random
                angle_offset = random.uniform(-0.5, 0.5)
                speed = math.sqrt(vel_x**2 + vel_y**2)
                current_angle = math.atan2(vel_y, vel_x)
                new_angle = current_angle + math.pi + angle_offset  # Add pi for 180 degree flip

                projectile.dx = math.cos(new_angle) * speed
                projectile.dy = math.sin(new_angle) * speed

            return {'deflected': True}

        return {'deflected': False}

    def apply_freeze(self, duration: int, complete_freeze: bool = False):
        """Apply freeze with resistance"""
        reduced_duration = int(duration * (1 - self.freeze_resistance))
        super().apply_freeze(reduced_duration, complete_freeze)

    def apply_wet_status(self, duration: int, lightning_multiplier: float = 2.0):
        """Apply wet status with resistance"""
        reduced_duration = int(duration * (1 - self.wet_resistance))
        super().apply_wet_status(reduced_duration, lightning_multiplier)

    def get_summoning_info(self):
        """Get information about enemy summoning for game loop"""
        if hasattr(self, 'should_summon_enemies') and self.should_summon_enemies > 0:
            count = self.should_summon_enemies
            self.should_summon_enemies = 0  # Reset after reading
            return count
        return 0

    def should_spawn_minions(self):
        """Check if boss should spawn minions (used by game loop)"""
        return hasattr(self, 'should_summon_enemies') and self.should_summon_enemies > 0

    def spawn_minions(self):
        """Spawn minion enemies (called by game loop)"""
        if not hasattr(self, 'should_summon_enemies') or self.should_summon_enemies <= 0:
            return []

        minion_count = self.should_summon_enemies
        self.should_summon_enemies = 0  # Reset after spawning

        spawned_minions = []

        # Import available enemy types
        try:
            from . import BasicEnemy, FastEnemy, TankEnemy
            available_enemies = [BasicEnemy, FastEnemy, TankEnemy]

            # Add more enemy types based on phase
            if self.phase >= 2:
                try:
                    from . import ShieldedEnemy, InvisibleEnemy
                    available_enemies.extend([ShieldedEnemy, InvisibleEnemy])
                except ImportError:
                    pass

            if self.phase >= 3:
                try:
                    from . import FlyingEnemy, RegeneratingEnemy
                    available_enemies.extend([FlyingEnemy, RegeneratingEnemy])
                except ImportError:
                    pass

        except ImportError:
            # Fallback to basic enemy only
            from . import BasicEnemy
            available_enemies = [BasicEnemy]

        import random
        for _ in range(minion_count):
            # Choose random enemy type
            enemy_class = random.choice(available_enemies)

            # Create enemy with boss's path and wave number
            minion = enemy_class(self.path, getattr(self, 'wave_number', 1))

            # Position minion near the boss but on the path
            if self.path and len(self.path) > 0:
                # Find closest path point to boss
                closest_distance = float('inf')
                closest_index = 0

                for i, (px, py) in enumerate(self.path):
                    distance = math.sqrt((self.x - px)**2 + (self.y - py)**2)
                    if distance < closest_distance:
                        closest_distance = distance
                        closest_index = i

                # Position minion at closest path point with some offset
                path_x, path_y = self.path[closest_index]
                offset_x = random.uniform(-30, 30)
                offset_y = random.uniform(-30, 30)

                minion.x = path_x + offset_x
                minion.y = path_y + offset_y
                minion.path_index = closest_index

                # Scale minion based on boss phase
                minion.health = int(minion.health * (0.5 + self.phase * 0.2))
                minion.max_health = minion.health

                spawned_minions.append(minion)

        return spawned_minions

    def draw(self, screen):
        """Draw the TimeLord Boss with new abilities"""
        # Draw deflection field
        if self.deflection_active:
            deflection_color = (255, 215, 0, 80)  # Semi-transparent gold
            deflection_surface = pygame.Surface((self.deflection_radius * 2,
                                                 self.deflection_radius * 2),
                                                pygame.SRCALPHA)
            pygame.draw.circle(deflection_surface, deflection_color,
                               (self.deflection_radius, self.deflection_radius),
                               self.deflection_radius, 5)
            screen.blit(deflection_surface,
                        (self.x - self.deflection_radius,
                         self.y - self.deflection_radius))

        # Draw deflection particles
        for particle in self.deflection_particles:
            alpha = particle['life'] / particle['max_life']
            size = max(1, int(3 * alpha))
            pygame.draw.circle(screen, particle['color'],
                               (int(particle['x']), int(particle['y'])), size)

        # Draw phase shift particles
        for particle in self.phase_particles:
            alpha = particle['life'] / particle['max_life']
            size = max(1, int(4 * alpha))
            color = tuple(int(c * alpha) for c in particle['color'])
            pygame.draw.circle(screen, color,
                               (int(particle['x']), int(particle['y'])), size)

        # Draw rotating aura (changes based on active abilities)
        aura_points = []
        for i in range(8):
            angle = self.aura_rotation + i * 45
            rad = math.radians(angle)
            aura_x = self.x + math.cos(rad) * (self.size + 15)
            aura_y = self.y + math.sin(rad) * (self.size + 15)
            aura_points.append((int(aura_x), int(aura_y)))

        # Aura color changes based on active abilities
        aura_color = (150, 0, 255)  # Default purple
        if self.phase_shift_active:
            aura_color = (255, 255, 255)  # White for phase shift
        elif self.deflection_active:
            aura_color = (255, 215, 0)  # Gold for deflection

        # Draw aura lines
        for i in range(len(aura_points)):
            next_i = (i + 1) % len(aura_points)
            pygame.draw.line(screen, aura_color,
                             aura_points[i], aura_points[next_i], 2)

        # Draw main boss body with phase colors
        boss_colors = [
            (75, 0, 130),    # Phase 1: Indigo
            (128, 0, 255),   # Phase 2: Purple
            (255, 0, 255),   # Phase 3: Magenta
            (255, 255, 255)  # Phase 4: White (ultimate)
        ]
        boss_color = boss_colors[self.phase - 1]

        # Pulsing effect based on abilities
        pulse_size = self.size
        if self.phase_shift_active:
            pulse_size += int(math.sin(self.pulse_timer * 2) * 3)
        elif self.deflection_active:
            pulse_size += int(math.sin(self.pulse_timer) * 5)

        pygame.draw.circle(screen, boss_color,
                           (int(self.x), int(self.y)), pulse_size)
        pygame.draw.circle(screen, (255, 255, 255),
                           (int(self.x), int(self.y)), pulse_size, 4)

        # Draw phase indicators (clock-like)
        for i in range(self.phase):
            angle = (i * 90) * math.pi / 180  # 4 quarters like a clock
            indicator_x = self.x + math.cos(angle) * (self.size - 8)
            indicator_y = self.y + math.sin(angle) * (self.size - 8)
            pygame.draw.circle(screen, (255, 255, 0),
                               (int(indicator_x), int(indicator_y)), 5)

        # Draw ability symbols based on phase
        ability_symbols = ['◊', '⚡', '👥', '⚔']  # Phase shift, deflection, summon, ultimate
        symbol_color = (255, 255, 255)
        if self.phase >= 4:
            symbol_color = (255, 255, 0)

        # Change symbol color based on active abilities
        if self.phase_shift_active:
            symbol_color = (255, 255, 255)  # White for phase shift
        elif self.deflection_active:
            symbol_color = (255, 215, 0)  # Gold for deflection

        font = pygame.font.Font(None, 24)
        symbol_text = font.render(
            ability_symbols[min(self.phase - 1, 3)], True, symbol_color)
        symbol_rect = symbol_text.get_rect(center=(int(self.x), int(self.y)))
        screen.blit(symbol_text, symbol_rect)

        # Draw immunity indicators (phase 3+)
        if self.phase >= 3 and self.immune_tower_types:
            immunity_y = self.y + self.size + 15
            immunity_text = f"Immune: {', '.join(self.immune_tower_types[:2])}"
            immunity_font = pygame.font.Font(None, 16)
            immunity_surface = immunity_font.render(immunity_text, True, (255, 100, 100))
            immunity_rect = immunity_surface.get_rect(center=(int(self.x), int(immunity_y)))
            screen.blit(immunity_surface, immunity_rect)

        # Draw massive health bar
        bar_width = self.size * 4
        bar_height = 12

        # Background
        pygame.draw.rect(screen, (50, 0, 50),
                         (self.x - bar_width//2, self.y - self.size - 25, bar_width, bar_height))

        # Health
        health_percentage = self.health / self.max_health
        health_color = boss_colors[self.phase - 1]
        pygame.draw.rect(screen, health_color,
                         (self.x - bar_width//2, self.y - self.size - 25,
                          int(bar_width * health_percentage), bar_height))

        # Phase markers
        for i in range(1, self.max_phases):
            marker_x = self.x - bar_width//2 + \
                (bar_width * (self.max_phases - i) / self.max_phases)
            pygame.draw.line(screen, (255, 255, 255),
                             (marker_x, self.y - self.size - 25),
                             (marker_x, self.y - self.size - 13), 3)

        # Draw boss title
        font = pygame.font.Font(None, 28)
        title_text = font.render("TIMELORD BOSS", True, (255, 255, 255))
        title_rect = title_text.get_rect(
            center=(self.x, self.y - self.size - 45))
        screen.blit(title_text, title_rect)
