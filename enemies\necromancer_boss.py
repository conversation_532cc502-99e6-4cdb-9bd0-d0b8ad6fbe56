from .enemy import Enemy
import pygame
import math
import random


class Necromancer<PERSON><PERSON>(Enemy):
    """Ultra powerful boss that manipulates death and undeath"""

    def __init__(self, path, wave_number=1):
        super().__init__(path, wave_number)
        self.health = 833  # Reduced from 2500 to 1/3
        self.max_health = 833
        self.speed = 0.6
        self.reward = 750
        self.color = (139, 69, 19)  # Dark brown/black
        self.size = 32

        # Boss damage to player (how many lives lost when reaching end)
        self.damage = 3  # Necromancer Boss deals 3 damage to player

        # Undead minion summoning (WORKING ABILITY)
        self.summon_timer = 0
        self.summon_cooldown = 600  # 10 seconds
        self.max_undead_minions = 6
        self.current_undead_count = 0

        # Summoning animation system
        self.is_summoning = False
        self.summoning_duration = 180  # 3 seconds of summoning animation
        self.summoning_timer = 0
        self.pending_summons = []  # List of enemies waiting to be spawned
        self.summon_portals = []  # Visual portals for each summon location

        # Death aura that buffs nearby enemies (WORKING ABILITY)
        self.death_aura_timer = 0
        self.death_aura_interval = 60  # Every second
        self.death_aura_radius = 100
        self.aura_speed_buff = 1.3  # Speed up enemies to 130% speed
        self.aura_strength_buff = 1.2  # Increase enemy damage resistance by 20%

        # Soul harvest ability (WORKING ABILITY - builds up to mass summoning)
        self.soul_harvest_timer = 0
        self.base_soul_harvest_cooldown = 300  # 5 seconds base cooldown
        self.soul_harvest_cooldown = self.base_soul_harvest_cooldown
        self.soul_harvest_radius = 120
        self.souls_harvested = 0
        self.max_souls = 10
        self.soul_army_summoned = False  # Track if mass summoning has been triggered
        self.soul_reset_timer = 0  # Timer for resetting souls after army summoning
        self.soul_reset_delay = 120  # 2 seconds delay before restarting soul collection

        # Boss resistances
        self.damage_reduction = 0.45  # Takes 55% damage
        self.poison_immunity = True
        # Heals for 15% of damage dealt (reduced from 20%)
        self.life_steal = 0.15

        # BALANCE FIX: Override immunities to ensure freeze effects can work
        # Bosses should never be completely immune to crowd control
        self.immunities['freeze_immune'] = False

        # Phase system
        self.phase = 1
        self.max_phases = 3

        # Visual effects
        self.dark_particles = []
        self.soul_orbs = []
        self.aura_pulse = 0
        self.floating_offset = 0

        # Track enemies affected by death aura for performance
        self.aura_affected_enemies = set()

    def update(self):
        """Update with necromancy abilities"""
        # Update phase based on health
        health_percentage = self.health / self.max_health
        if health_percentage > 0.66:
            self.phase = 1
        elif health_percentage > 0.33:
            self.phase = 2
        else:
            self.phase = 3  # Final phase - most dangerous

        # Phase-based abilities
        if self.phase >= 2:
            self.death_aura_interval = 40  # Faster death aura
        if self.phase >= 3:
            self.max_undead_minions = 10  # More minions
            self.aura_speed_buff = 1.5  # Stronger speed buff

        # Health-based soul collection speed scaling
        health_percentage = self.health / self.max_health
        if health_percentage > 0.66:
            # Phase 1: 50 seconds (5 seconds * 10 souls)
            self.soul_harvest_cooldown = self.base_soul_harvest_cooldown  # 300 frames = 5 seconds
        elif health_percentage > 0.33:
            # Phase 2: 20 seconds (2 seconds * 10 souls)
            self.soul_harvest_cooldown = 120  # 2 seconds
        else:
            # Phase 3: 10 seconds (1 second * 10 souls)
            self.soul_harvest_cooldown = 60  # 1 second

        # Handle summoning animation (boss stays stationary during summoning)
        if self.is_summoning:
            self.summoning_timer += 1
            self.update_summoning_animation()
            # Don't move during summoning - skip parent update
        else:
            super().update()

        # Update ability timers
        self.death_aura_timer += 1
        if not self.is_summoning:  # Don't advance summon timer during summoning
            self.summon_timer += 1
        self.soul_harvest_timer += 1
        self.soul_reset_timer += 1  # Update soul reset timer
        self.aura_pulse += 0.1
        self.floating_offset += 0.05

        # Trigger abilities
        self.update_necromancy_abilities()

        # Update visual effects
        self.update_dark_particles()
        self.update_soul_orbs()
        self.update_summon_portals()

    def update_with_speed(self, speed_multiplier: float):
        """Update with speed multiplier for performance optimization"""
        # Update phase based on health
        health_percentage = self.health / self.max_health
        if health_percentage > 0.66:
            self.phase = 1
        elif health_percentage > 0.33:
            self.phase = 2
        else:
            self.phase = 3  # Final phase - most dangerous

        # Phase-based abilities
        if self.phase >= 2:
            self.death_aura_interval = 40  # Faster death aura
        if self.phase >= 3:
            self.max_undead_minions = 10  # More minions
            self.aura_speed_buff = 1.5  # Stronger speed buff

        # Health-based soul collection speed scaling
        health_percentage = self.health / self.max_health
        if health_percentage > 0.66:
            # Phase 1: 50 seconds (5 seconds * 10 souls)
            self.soul_harvest_cooldown = self.base_soul_harvest_cooldown  # 300 frames = 5 seconds
        elif health_percentage > 0.33:
            # Phase 2: 20 seconds (2 seconds * 10 souls)
            self.soul_harvest_cooldown = 120  # 2 seconds
        else:
            # Phase 3: 10 seconds (1 second * 10 souls)
            self.soul_harvest_cooldown = 60  # 1 second

        # Handle summoning animation (boss stays stationary during summoning)
        if self.is_summoning:
            self.summoning_timer += speed_multiplier
            self.update_summoning_animation()
            # Don't move during summoning - skip parent update
        else:
            super().update_with_speed(speed_multiplier)

        # Update ability timers with speed multiplier
        self.death_aura_timer += speed_multiplier
        if not self.is_summoning:  # Don't advance summon timer during summoning
            self.summon_timer += speed_multiplier
        self.soul_harvest_timer += speed_multiplier
        self.soul_reset_timer += speed_multiplier  # Update soul reset timer
        self.aura_pulse += 0.1 * speed_multiplier
        self.floating_offset += 0.05 * speed_multiplier

        # Trigger abilities
        self.update_necromancy_abilities()

        # Update visual effects
        self.update_dark_particles()
        self.update_soul_orbs()
        self.update_summon_portals()

    def update_necromancy_abilities(self):
        """Update necromancy-based abilities"""
        # Death aura (affects nearby enemies)
        if self.death_aura_timer >= self.death_aura_interval:
            self.pulse_death_aura()
            self.death_aura_timer = 0

        # Soul harvest (self-buff based on nearby enemies)
        if self.soul_harvest_timer >= self.soul_harvest_cooldown:
            self.harvest_souls()
            self.soul_harvest_timer = 0

        # Handle soul reset after army summoning
        if self.soul_army_summoned and self.soul_reset_timer >= self.soul_reset_delay:
            self.reset_soul_collection()

        # Note: Undead summoning is handled by should_summon_undead() method
        # which is called by the game loop

    def harvest_souls(self):
        """Harvest souls dynamically over time, trigger mass summoning at max souls"""
        # Only harvest if we haven't reached max or if we're in reset phase
        if self.souls_harvested < self.max_souls:
            self.souls_harvested += 1

            # Create soul harvest effect
            for _ in range(8):
                angle = random.uniform(0, 2 * math.pi)
                distance = random.uniform(20, self.soul_harvest_radius)
                particle_x = self.x + math.cos(angle) * distance
                particle_y = self.y + math.sin(angle) * distance

                particle = {
                    'x': particle_x,
                    'y': particle_y,
                    'vx': -math.cos(angle) * 2,  # Move toward boss
                    'vy': -math.sin(angle) * 2,
                    'life': 45,
                    'max_life': 45,
                    'color': (148, 0, 211),  # Purple soul energy
                    'type': 'soul_harvest'
                }
                self.dark_particles.append(particle)

            # Check if we've reached max souls
            if self.souls_harvested >= self.max_souls:
                self.trigger_soul_army_summoning()

    def trigger_soul_army_summoning(self):
        """Trigger massive summoning of 20 enemies when souls are maxed"""
        self.soul_army_summoned = True
        self.soul_reset_timer = 0  # Start reset timer

        # Force start summoning animation for soul army
        if not self.is_summoning:
            self.is_summoning = True
            self.summoning_timer = 0
            self.pending_summons = []
            self.summon_portals = []

            # Create dramatic soul army summoning effect
            for _ in range(30):
                angle = random.uniform(0, 2 * math.pi)
                distance = random.uniform(10, 60)
                summon_x = self.x + math.cos(angle) * distance
                summon_y = self.y + math.sin(angle) * distance

                particle = {
                    'x': summon_x,
                    'y': summon_y,
                    'vx': random.uniform(-2, 2),
                    'vy': random.uniform(-2, 2),
                    'life': 80,
                    'max_life': 80,
                    'color': (148, 0, 211),  # Purple soul energy
                    'type': 'soul_army'
                }
                self.dark_particles.append(particle)

            # Create 20 portals for soul army summoning
            self.create_soul_army_portals()

    def reset_soul_collection(self):
        """Reset soul collection after 2 seconds delay"""
        self.souls_harvested = 0
        self.soul_army_summoned = False
        self.soul_reset_timer = 0

        # Create soul reset effect
        for _ in range(12):
            angle = random.uniform(0, 2 * math.pi)
            distance = random.uniform(10, 30)
            particle_x = self.x + math.cos(angle) * distance
            particle_y = self.y + math.sin(angle) * distance

            particle = {
                'x': particle_x,
                'y': particle_y,
                'vx': math.cos(angle) * 3,  # Move outward
                'vy': math.sin(angle) * 3,
                'life': 30,
                'max_life': 30,
                'color': (255, 255, 255),  # White for reset
                'type': 'soul_reset'
            }
            self.dark_particles.append(particle)

    def create_soul_army_portals(self):
        """Create 20 portals for the soul army summoning"""
        # Get available enemy types (same as regular summoning)
        try:
            from . import (BasicEnemy, FastEnemy, TankEnemy, ShieldedEnemy,
                           InvisibleEnemy, FlyingEnemy, RegeneratingEnemy,
                           SplittingEnemy, TeleportingEnemy)
            basic_minions = [BasicEnemy, FastEnemy, TankEnemy, ShieldedEnemy,
                             InvisibleEnemy, FlyingEnemy, RegeneratingEnemy,
                             SplittingEnemy, TeleportingEnemy]
        except ImportError:
            from . import BasicEnemy, FastEnemy, TankEnemy
            basic_minions = [BasicEnemy, FastEnemy, TankEnemy]

        # Try to add advanced enemies
        try:
            from . import (ArmoredEnemy, EnergyShieldEnemy, GroundedEnemy,
                           FireElementalEnemy, ToxicEnemy, PhaseShiftEnemy,
                           BlastProofEnemy, SpectralEnemy, CrystallineEnemy,
                           ToxicMutantEnemy, VoidEnemy, AdaptiveEnemy)
            advanced_minions = [ArmoredEnemy, EnergyShieldEnemy, GroundedEnemy,
                                FireElementalEnemy, ToxicEnemy, PhaseShiftEnemy,
                                BlastProofEnemy, SpectralEnemy, CrystallineEnemy,
                                ToxicMutantEnemy, VoidEnemy, AdaptiveEnemy]
            available_minions = basic_minions + advanced_minions
        except ImportError:
            available_minions = basic_minions

        # Create 20 portals for soul army
        for i in range(20):
            # Choose random enemy type
            minion_class = random.choice(available_minions)

            # Calculate actual spawn position ON THE PATH (increased range for soul army)
            # Even larger range for soul army
            path_offset = random.uniform(-120, 120)
            # Larger perpendicular range
            perpendicular_offset_x = random.uniform(-60, 60)
            perpendicular_offset_y = random.uniform(-60, 60)

            # Calculate the actual spawn position where enemy will appear
            spawn_x = self.x + perpendicular_offset_x
            spawn_y = self.y + perpendicular_offset_y

            # Portal appears at the ACTUAL spawn location on the path
            portal_x = spawn_x
            portal_y = spawn_y

            # Create portal visual effect AT THE SPAWN LOCATION
            portal = {
                'x': portal_x,
                'y': portal_y,
                'size': 0,  # Starts small, grows during animation
                'max_size': 30,  # Larger portals for soul army
                'pulse': 0,
                'particles': [],
                'enemy_class': minion_class,
                'path_offset': path_offset,
                'perpendicular_offset_x': perpendicular_offset_x,
                'perpendicular_offset_y': perpendicular_offset_y,
                # Faster staggered spawning (every 9 frames for 20 enemies)
                'spawn_delay': i * 9,
                'is_soul_army': True  # Mark as soul army portal
            }

            # Create initial portal particles (more for soul army)
            for _ in range(15):
                particle = {
                    'x': portal_x + random.uniform(-5, 5),
                    'y': portal_y + random.uniform(-5, 5),
                    'vx': random.uniform(-0.5, 0.5),
                    'vy': random.uniform(-0.5, 0.5),
                    'life': 150,  # Longer lasting for soul army
                    'max_life': 150,
                    'color': (148, 0, 211),  # Purple soul energy
                    'type': 'portal'
                }
                portal['particles'].append(particle)

            self.summon_portals.append(portal)

    def apply_death_aura_to_enemies(self, enemies):
        """Apply death aura buffs to nearby enemies (called by game loop)"""
        affected_this_frame = set()

        for enemy in enemies:
            if enemy == self:  # Don't affect self
                continue

            # Calculate distance
            distance = math.sqrt((enemy.x - self.x)**2 + (enemy.y - self.y)**2)

            if distance <= self.death_aura_radius:
                affected_this_frame.add(id(enemy))

                # Apply speed buff if not already applied
                if not hasattr(enemy, 'necromancer_aura_buffed'):
                    enemy.necromancer_aura_buffed = True
                    enemy.original_speed = enemy.speed
                    enemy.speed *= self.aura_speed_buff

                # Apply strength buff (increase damage resistance)
                if not hasattr(enemy, 'necromancer_aura_strengthened'):
                    enemy.necromancer_aura_strengthened = True
                    if hasattr(enemy, 'damage_reduction'):
                        enemy.original_damage_reduction = enemy.damage_reduction
                        # Increase damage reduction (make them tankier)
                        enemy.damage_reduction = min(
                            0.8, enemy.damage_reduction * self.aura_strength_buff)
                    else:
                        # Give damage reduction to enemies that don't have it
                        enemy.damage_reduction = 0.15  # 15% damage reduction
                        enemy.original_damage_reduction = 0

        # Remove effects from enemies no longer in range
        for enemy in enemies:
            if id(enemy) not in affected_this_frame:
                if hasattr(enemy, 'necromancer_aura_buffed'):
                    enemy.speed = enemy.original_speed
                    delattr(enemy, 'necromancer_aura_buffed')
                    delattr(enemy, 'original_speed')

                if hasattr(enemy, 'necromancer_aura_strengthened'):
                    if hasattr(enemy, 'original_damage_reduction'):
                        enemy.damage_reduction = enemy.original_damage_reduction
                        delattr(enemy, 'original_damage_reduction')
                    delattr(enemy, 'necromancer_aura_strengthened')

        self.aura_affected_enemies = affected_this_frame

    def pulse_death_aura(self):
        """Create empowerment aura visual effects"""
        # Create aura particles for visual effect
        for i in range(8):
            angle = (i * 45) * math.pi / 180
            particle_x = self.x + math.cos(angle) * self.death_aura_radius
            particle_y = self.y + math.sin(angle) * self.death_aura_radius

            particle = {
                'x': particle_x,
                'y': particle_y,
                'vx': math.cos(angle) * 1.5,
                'vy': math.sin(angle) * 1.5,
                'life': 40,
                'max_life': 40,
                'color': (200, 50, 0),  # Red/orange for empowerment
                'type': 'death_aura'
            }
            self.dark_particles.append(particle)

        # Note: Actual enemy effects are applied by apply_death_aura_to_enemies()
        # which should be called by the game loop

    def summon_undead_minion(self):
        """Start summoning animation - returns empty list initially"""
        if self.is_summoning:
            return []  # Already summoning

        self.summon_timer = 0
        self.is_summoning = True
        self.summoning_timer = 0
        self.pending_summons = []
        self.summon_portals = []

        # Create initial summoning effect around boss
        for _ in range(20):
            angle = random.uniform(0, 2 * math.pi)
            distance = random.uniform(10, 40)
            summon_x = self.x + math.cos(angle) * distance
            summon_y = self.y + math.sin(angle) * distance

            particle = {
                'x': summon_x,
                'y': summon_y,
                'vx': random.uniform(-1, 1),
                'vy': random.uniform(-1, 1),
                'life': 60,
                'max_life': 60,
                'color': (75, 0, 130),  # Dark purple
                'type': 'summon'
            }
            self.dark_particles.append(particle)

        # Prepare enemies to be summoned (but don't spawn them yet)
        # Use MegaBoss-style spawning - can spawn ANY enemy type
        # Start with basic enemies that we know work
        try:
            from . import (BasicEnemy, FastEnemy, TankEnemy, ShieldedEnemy,
                           InvisibleEnemy, FlyingEnemy, RegeneratingEnemy,
                           SplittingEnemy, TeleportingEnemy)
            basic_minions = [BasicEnemy, FastEnemy, TankEnemy, ShieldedEnemy,
                             InvisibleEnemy, FlyingEnemy, RegeneratingEnemy,
                             SplittingEnemy, TeleportingEnemy]
        except ImportError:
            # Fallback to just basic enemies if others fail
            from . import BasicEnemy, FastEnemy, TankEnemy
            basic_minions = [BasicEnemy, FastEnemy, TankEnemy]

        # Try to add advanced enemies
        try:
            from . import (ArmoredEnemy, EnergyShieldEnemy, GroundedEnemy,
                           FireElementalEnemy, ToxicEnemy, PhaseShiftEnemy,
                           BlastProofEnemy, SpectralEnemy, CrystallineEnemy,
                           ToxicMutantEnemy, VoidEnemy, AdaptiveEnemy)
            advanced_minions = [ArmoredEnemy, EnergyShieldEnemy, GroundedEnemy,
                                FireElementalEnemy, ToxicEnemy, PhaseShiftEnemy,
                                BlastProofEnemy, SpectralEnemy, CrystallineEnemy,
                                ToxicMutantEnemy, VoidEnemy, AdaptiveEnemy]
            available_minions = basic_minions + advanced_minions
        except ImportError:
            # Use only basic enemies if advanced ones fail
            available_minions = basic_minions

        minion_count = self.get_minion_count()

        # Create portals and prepare enemies for each summon location
        for i in range(minion_count):
            # Choose random enemy type
            minion_class = random.choice(available_minions)

            # Calculate actual spawn position ON THE PATH (increased range)
            # Increased from -30,30 to -80,80
            path_offset = random.uniform(-80, 80)
            # Increased from -15,15 to -40,40
            perpendicular_offset_x = random.uniform(-40, 40)
            # Increased from -15,15 to -40,40
            perpendicular_offset_y = random.uniform(-40, 40)

            # Calculate the actual spawn position where enemy will appear
            spawn_x = self.x + perpendicular_offset_x
            spawn_y = self.y + perpendicular_offset_y

            # Portal appears at the ACTUAL spawn location on the path
            portal_x = spawn_x
            portal_y = spawn_y

            # Create portal visual effect AT THE SPAWN LOCATION
            portal = {
                'x': portal_x,
                'y': portal_y,
                'size': 0,  # Starts small, grows during animation
                'max_size': 25,
                'pulse': 0,
                'particles': [],
                'enemy_class': minion_class,
                'path_offset': path_offset,  # Store for proper path spawning
                'perpendicular_offset_x': perpendicular_offset_x,
                'perpendicular_offset_y': perpendicular_offset_y,
                'spawn_delay': i * 30  # Stagger spawns
            }

            # Create initial portal particles
            for _ in range(10):
                particle = {
                    'x': portal_x + random.uniform(-5, 5),
                    'y': portal_y + random.uniform(-5, 5),
                    'vx': random.uniform(-0.5, 0.5),
                    'vy': random.uniform(-0.5, 0.5),
                    'life': 120,
                    'max_life': 120,
                    'color': (148, 0, 211),  # Purple portal energy
                    'type': 'portal'
                }
                portal['particles'].append(particle)

            self.summon_portals.append(portal)

        return []  # Don't spawn enemies immediately

    def get_minion_count(self):
        """Get number of minions to spawn based on phase (like MegaBoss)"""
        return self.phase + 1  # Phase 1: 2 minions, Phase 2: 3 minions, Phase 3: 4 minions

    def should_summon_undead(self):
        """Check if boss should summon undead minions - returns list of enemies to spawn"""
        # Return any completed summons (both regular and soul army)
        if self.pending_summons:
            completed_summons = self.pending_summons[:]
            self.pending_summons = []
            return completed_summons

        # Start new regular summoning if ready (but not during soul army)
        if (self.summon_timer >= self.summon_cooldown and
                self.current_undead_count < self.max_undead_minions and
                not self.is_summoning and
                not self.soul_army_summoned):  # Don't regular summon during soul army reset
            self.summon_undead_minion()
        return []

    def update_summoning_animation(self):
        """Update the summoning animation and spawn enemies when ready"""
        if self.summoning_timer >= self.summoning_duration:
            # Summoning finished - finalize all remaining portals
            for portal in self.summon_portals:
                if portal['enemy_class']:  # Not yet spawned
                    enemy = self.create_enemy_from_portal(portal)
                    if enemy:
                        self.pending_summons.append(enemy)

            # Reset summoning state
            self.is_summoning = False
            self.summoning_timer = 0
            self.summon_portals = []
            return

        # Update portals and spawn enemies at their designated times
        for portal in self.summon_portals:
            if portal['enemy_class'] and self.summoning_timer >= portal['spawn_delay']:
                # Time to spawn this enemy
                enemy = self.create_enemy_from_portal(portal)
                if enemy:
                    self.pending_summons.append(enemy)
                portal['enemy_class'] = None  # Mark as spawned

    def create_enemy_from_portal(self, portal):
        """Create an enemy from a portal using proper path-based spawning"""
        if not portal['enemy_class']:
            return None

        minion_class = portal['enemy_class']
        new_minion = minion_class(self.path, self.wave_number)

        # CRITICAL: Position minion at boss location on the path with stored offsets
        new_minion.path_index = self.path_index
        new_minion.distance_traveled = self.distance_traveled

        # Apply the stored path offset for proper path positioning
        new_minion.distance_traveled += portal['path_offset']

        # Position them at boss location with stored perpendicular offsets
        new_minion.x = self.x + portal['perpendicular_offset_x']
        new_minion.y = self.y + portal['perpendicular_offset_y']

        # Make minions slightly weaker than normal (like MegaBoss)
        new_minion.health = max(1, int(new_minion.health * 0.75))
        new_minion.max_health = new_minion.health
        new_minion.reward = int(new_minion.reward * 0.4)

        # Ensure they inherit path state properly
        new_minion.reached_end = False

        # Visual indicator that this is a summoned undead minion
        new_minion.color = (100, 50, 100)  # Dark purple tint

        self.current_undead_count += 1

        # Create spawn effect at the actual spawn location (not portal location)
        for _ in range(15):
            particle = {
                'x': new_minion.x + random.uniform(-10, 10),
                'y': new_minion.y + random.uniform(-10, 10),
                'vx': random.uniform(-3, 3),
                'vy': random.uniform(-3, 3),
                'life': 30,
                'max_life': 30,
                'color': (100, 50, 100),  # Match minion color
                'type': 'spawn_burst'
            }
            self.dark_particles.append(particle)

        return new_minion

    def update_summon_portals(self):
        """Update summoning portal visual effects"""
        for portal in self.summon_portals:
            # Grow portal size over time
            if portal['size'] < portal['max_size']:
                portal['size'] += 0.5

            # Update pulse effect
            portal['pulse'] += 0.2

            # Update portal particles
            for particle in portal['particles'][:]:
                # Spiral particles around portal center
                dx = particle['x'] - portal['x']
                dy = particle['y'] - portal['y']
                distance = math.sqrt(dx**2 + dy**2)

                if distance > 0:
                    # Create spiral motion
                    angle = math.atan2(dy, dx) + 0.1
                    new_distance = max(5, distance - 0.5)  # Spiral inward
                    particle['x'] = portal['x'] + \
                        math.cos(angle) * new_distance
                    particle['y'] = portal['y'] + \
                        math.sin(angle) * new_distance

                particle['life'] -= 1
                if particle['life'] <= 0:
                    portal['particles'].remove(particle)

            # Add new particles to keep portal active
            if len(portal['particles']) < 8:
                angle = random.uniform(0, 2 * math.pi)
                distance = random.uniform(15, 30)
                particle = {
                    'x': portal['x'] + math.cos(angle) * distance,
                    'y': portal['y'] + math.sin(angle) * distance,
                    'vx': 0,
                    'vy': 0,
                    'life': 60,
                    'max_life': 60,
                    'color': (148, 0, 211),  # Purple portal energy
                    'type': 'portal'
                }
                portal['particles'].append(particle)

    def minion_died(self):
        """Called when an undead minion dies"""
        self.current_undead_count = max(0, self.current_undead_count - 1)

    def update_dark_particles(self):
        """Update dark magic particle effects"""
        for particle in self.dark_particles[:]:
            # Different behavior based on particle type
            if particle['type'] == 'soul_harvest':
                # Soul harvest particles move toward boss
                dx = self.x - particle['x']
                dy = self.y - particle['y']
                distance = math.sqrt(dx**2 + dy**2)
                if distance > 0:
                    particle['vx'] = dx / distance * 3
                    particle['vy'] = dy / distance * 3
            elif particle['type'] == 'death_aura':
                # Death aura particles expand outward and fade
                particle['vx'] *= 0.95  # Slow down over time
                particle['vy'] *= 0.95
            elif particle['type'] == 'summon':
                # Summon particles swirl around
                particle['vx'] += random.uniform(-0.5, 0.5)
                particle['vy'] += random.uniform(-0.5, 0.5)
            elif particle['type'] == 'portal':
                # Portal particles handled by update_summon_portals
                pass
            elif particle['type'] == 'spawn_burst':
                # Spawn burst particles expand outward
                particle['vx'] *= 0.9  # Slow down over time
                particle['vy'] *= 0.9
            elif particle['type'] == 'soul_army':
                # Soul army particles swirl dramatically
                particle['vx'] += random.uniform(-1, 1)
                particle['vy'] += random.uniform(-1, 1)
            elif particle['type'] == 'soul_reset':
                # Soul reset particles expand outward
                particle['vx'] *= 0.95  # Slow down over time
                particle['vy'] *= 0.95

            particle['x'] += particle['vx']
            particle['y'] += particle['vy']
            particle['life'] -= 1

            if particle['life'] <= 0:
                self.dark_particles.remove(particle)

    def update_soul_orbs(self):
        """Update floating soul orbs around boss"""
        # Maintain 3-6 soul orbs based on phase
        target_orbs = 3 + self.phase

        while len(self.soul_orbs) < target_orbs:
            orb = {
                'angle': random.uniform(0, 360),
                'radius': self.size + random.uniform(20, 40),
                'speed': random.uniform(0.5, 1.5),
                'size': random.randint(3, 8),
                'color': (100, 0, 100)
            }
            self.soul_orbs.append(orb)

        # Update existing orbs
        for orb in self.soul_orbs:
            orb['angle'] += orb['speed']
            if orb['angle'] >= 360:
                orb['angle'] -= 360

    def take_damage(self, damage, tower_type: str = 'basic'):
        """Take damage with necromancer resistances and soul power"""
        # Poison immunity
        if tower_type == 'poison':
            return 0

        # Apply summoning vulnerability - 2x damage while summoning
        if self.is_summoning:
            damage *= 2.0

        # Apply 3x damage multiplier for frozen enemies
        if self.frozen:
            damage = int(damage * 3.0)

        # Apply damage reduction
        reduced_damage = damage * (1 - self.damage_reduction)
        actual_damage = min(reduced_damage, self.health)

        # Life steal - heal for portion of damage taken
        life_steal_amount = actual_damage * self.life_steal
        self.health = min(self.max_health, self.health +
                          life_steal_amount - reduced_damage)

        return actual_damage

    def is_in_death_aura_range(self, x, y):
        """Check if coordinates are within death aura range"""
        distance = math.sqrt((x - self.x)**2 + (y - self.y)**2)
        return distance <= self.death_aura_radius

    def draw(self, screen):
        """Draw the Necromancer Boss with dark magic effects"""
        # Calculate floating position
        float_y = self.y + math.sin(self.floating_offset) * 3

        # Draw empowerment aura with enhanced visuals (red/orange for buffing)
        aura_alpha = int(80 + math.sin(self.aura_pulse) * 40)
        aura_color = (200, 50, 0, aura_alpha)  # Red/orange for empowerment
        aura_surface = pygame.Surface((self.death_aura_radius * 2,
                                       self.death_aura_radius * 2),
                                      pygame.SRCALPHA)
        pygame.draw.circle(aura_surface, aura_color,
                           (self.death_aura_radius, self.death_aura_radius),
                           self.death_aura_radius)
        screen.blit(aura_surface,
                    (self.x - self.death_aura_radius,
                     float_y - self.death_aura_radius))

        # Draw soul harvest field when active
        if self.soul_harvest_timer >= self.soul_harvest_cooldown - 60:  # Show for 1 second before harvest
            harvest_alpha = int(60 + math.sin(self.aura_pulse * 2) * 30)
            harvest_color = (148, 0, 211, harvest_alpha)  # Purple
            harvest_surface = pygame.Surface((self.soul_harvest_radius * 2,
                                              self.soul_harvest_radius * 2),
                                             pygame.SRCALPHA)
            pygame.draw.circle(harvest_surface, harvest_color,
                               (self.soul_harvest_radius,
                                self.soul_harvest_radius),
                               self.soul_harvest_radius)
            screen.blit(harvest_surface,
                        (self.x - self.soul_harvest_radius,
                         float_y - self.soul_harvest_radius))

        # Draw dark particles
        for particle in self.dark_particles:
            alpha = particle['life'] / particle['max_life']
            size = max(1, int(5 * alpha))

            # Different colors and effects for different particle types
            color = particle['color']
            if particle['type'] == 'soul_harvest':
                # Pulsing purple effect
                pulse = int(
                    255 * (0.6 + 0.4 * math.sin(pygame.time.get_ticks() * 0.008)))
                color = (pulse, 0, pulse)
                size = max(2, int(6 * alpha))  # Slightly larger
            elif particle['type'] == 'death_aura':
                # Fading red/orange empowerment effect
                fade = int(200 * alpha), int(50 * alpha), int(0 * alpha)
                color = fade
            elif particle['type'] == 'summon':
                # Swirling purple effect
                swirl = int(
                    75 * (0.8 + 0.2 * math.sin(pygame.time.get_ticks() * 0.01)))
                color = (swirl, 0, 130)
            elif particle['type'] == 'soul_army':
                # Dramatic soul army effect - bright pulsing purple
                pulse = int(
                    200 * (0.7 + 0.3 * math.sin(pygame.time.get_ticks() * 0.005)))
                color = (pulse, 0, pulse)
                size = max(3, int(8 * alpha))  # Larger particles
            elif particle['type'] == 'soul_reset':
                # Soul reset effect - bright white expanding
                white_intensity = int(255 * alpha)
                color = (white_intensity, white_intensity, white_intensity)
                size = max(2, int(5 * alpha))

            pygame.draw.circle(screen, color,
                               (int(particle['x']), int(particle['y'])), size)

        # Draw summoning portals
        for portal in self.summon_portals:
            if portal['size'] > 0:
                # Draw portal base
                portal_alpha = int(150 + math.sin(portal['pulse']) * 50)
                portal_color = (148, 0, 211, portal_alpha)  # Purple
                portal_surface = pygame.Surface(
                    (portal['size'] * 2, portal['size'] * 2), pygame.SRCALPHA)
                pygame.draw.circle(portal_surface, portal_color,
                                   (int(portal['size']), int(portal['size'])), int(portal['size']))
                screen.blit(portal_surface,
                            (portal['x'] - portal['size'], portal['y'] - portal['size']))

                # Draw portal ring
                ring_color = (200, 100, 255)
                pygame.draw.circle(screen, ring_color,
                                   (int(portal['x']), int(portal['y'])), int(portal['size']), 3)

                # Draw inner swirl
                swirl_size = int(portal['size'] * 0.6)
                swirl_alpha = int(100 + math.sin(portal['pulse'] * 2) * 30)
                swirl_color = (255, 150, 255, swirl_alpha)
                swirl_surface = pygame.Surface(
                    (swirl_size * 2, swirl_size * 2), pygame.SRCALPHA)
                pygame.draw.circle(swirl_surface, swirl_color,
                                   (swirl_size, swirl_size), swirl_size)
                screen.blit(swirl_surface,
                            (portal['x'] - swirl_size, portal['y'] - swirl_size))

                # Draw portal particles
                for particle in portal['particles']:
                    particle_alpha = particle['life'] / particle['max_life']
                    particle_size = max(1, int(3 * particle_alpha))
                    particle_color = (int(148 * particle_alpha),
                                      0, int(211 * particle_alpha))
                    pygame.draw.circle(screen, particle_color,
                                       (int(particle['x']), int(particle['y'])), particle_size)

        # Draw floating soul orbs
        for orb in self.soul_orbs:
            orb_x = self.x + \
                math.cos(math.radians(orb['angle'])) * orb['radius']
            orb_y = float_y + \
                math.sin(math.radians(orb['angle'])) * orb['radius']

            # Pulsing soul orb
            pulse_size = orb['size'] + \
                int(math.sin(pygame.time.get_ticks()
                    * 0.005 + orb['angle']) * 2)
            pygame.draw.circle(screen, orb['color'],
                               (int(orb_x), int(orb_y)), pulse_size)
            pygame.draw.circle(screen, (200, 100, 200),
                               (int(orb_x), int(orb_y)), pulse_size, 1)

        # Draw main boss body with phase colors
        boss_colors = [
            (139, 69, 19),   # Phase 1: Dark brown
            (75, 0, 130),    # Phase 2: Dark purple
            (0, 0, 0)        # Phase 3: Black
        ]
        boss_color = boss_colors[self.phase - 1]

        # Draw necromancer robe (larger circle)
        robe_size = self.size + 8
        pygame.draw.circle(screen, boss_color,
                           (int(self.x), int(float_y)), robe_size)

        # Draw inner dark core
        pygame.draw.circle(screen, (50, 0, 50),
                           (int(self.x), int(float_y)), self.size)
        pygame.draw.circle(screen, (255, 255, 255),
                           (int(self.x), int(float_y)), self.size, 3)

        # Draw phase indicators (skull symbols)
        for i in range(self.phase):
            angle = (i * 120) * math.pi / 180  # 3 positions around boss
            indicator_x = self.x + math.cos(angle) * (self.size - 5)
            indicator_y = float_y + math.sin(angle) * (self.size - 5)

            # Draw mini skull
            pygame.draw.circle(screen, (255, 255, 255),
                               (int(indicator_x), int(indicator_y)), 4)
            pygame.draw.circle(screen, (0, 0, 0),
                               (int(indicator_x - 1), int(indicator_y - 1)), 1)
            pygame.draw.circle(screen, (0, 0, 0),
                               (int(indicator_x + 1), int(indicator_y - 1)), 1)

        # Draw necromancer staff
        staff_length = self.size + 15
        staff_angle = math.sin(self.floating_offset) * 0.2  # Slight sway
        staff_end_x = self.x + math.cos(staff_angle) * staff_length
        staff_end_y = float_y + math.sin(staff_angle) * staff_length

        pygame.draw.line(screen, (101, 67, 33),
                         (int(self.x), int(float_y)),
                         (int(staff_end_x), int(staff_end_y)), 4)

        # Draw staff crystal
        crystal_color = (148, 0, 211) if self.phase < 3 else (255, 0, 255)
        pygame.draw.circle(screen, crystal_color,
                           (int(staff_end_x), int(staff_end_y)), 6)

        # Draw necromancy symbol in center
        symbol_color = (255, 255, 255) if self.phase < 3 else (255, 0, 0)
        font = pygame.font.Font(None, 20)
        symbol_text = font.render("☠", True, symbol_color)  # Skull symbol
        symbol_rect = symbol_text.get_rect(center=(int(self.x), int(float_y)))
        screen.blit(symbol_text, symbol_rect)

        # Draw massive health bar
        bar_width = self.size * 4
        bar_height = 12

        # Background
        pygame.draw.rect(screen, (50, 0, 50),
                         (self.x - bar_width//2, float_y - self.size - 30, bar_width, bar_height))

        # Health
        health_percentage = self.health / self.max_health
        health_color = boss_colors[self.phase - 1]
        pygame.draw.rect(screen, health_color,
                         (self.x - bar_width//2, float_y - self.size - 30,
                          int(bar_width * health_percentage), bar_height))

        # Phase markers
        for i in range(1, self.max_phases):
            marker_x = self.x - bar_width//2 + \
                (bar_width * (self.max_phases - i) / self.max_phases)
            pygame.draw.line(screen, (255, 255, 255),
                             (marker_x, float_y - self.size - 30),
                             (marker_x, float_y - self.size - 18), 3)

        # Draw boss title
        font = pygame.font.Font(None, 28)
        title_text = font.render("NECROMANCER OVERLORD", True, (255, 255, 255))
        title_rect = title_text.get_rect(
            center=(self.x, float_y - self.size - 50))
        screen.blit(title_text, title_rect)

        # Draw souls harvested indicator
        if self.souls_harvested > 0 or self.soul_army_summoned:
            souls_font = pygame.font.Font(None, 20)
            # Change color and text based on soul state
            if self.souls_harvested >= self.max_souls:
                if self.soul_army_summoned:
                    # Show reset countdown
                    reset_progress = self.soul_reset_timer / self.soul_reset_delay
                    if reset_progress < 1.0:
                        souls_color = (255, 100, 100)  # Light red during reset
                        # Convert to seconds
                        reset_time = (self.soul_reset_delay -
                                      self.soul_reset_timer) / 60.0
                        souls_text = souls_font.render(
                            f"RESETTING... {reset_time:.1f}s", True, souls_color)
                    else:
                        # White when reset complete
                        souls_color = (255, 255, 255)
                        souls_text = souls_font.render(
                            "SOULS RESET!", True, souls_color)
                else:
                    souls_color = (255, 255, 0)  # Yellow when ready to summon
                    souls_text = souls_font.render(
                        "SOUL ARMY READY!", True, souls_color)
            else:
                souls_color = (148, 0, 211)  # Purple for normal
                souls_text = souls_font.render(
                    f"Souls: {self.souls_harvested}/{self.max_souls}", True, souls_color)

            souls_rect = souls_text.get_rect(
                center=(self.x, float_y - self.size - 70))
            screen.blit(souls_text, souls_rect)

        # Draw summoning indicator with vulnerability warning
        if self.is_summoning:
            summon_font = pygame.font.Font(None, 24)
            progress = self.summoning_timer / self.summoning_duration
            summon_text = summon_font.render(
                f"SUMMONING... {int(progress * 100)}%", True, (255, 100, 255))
            summon_rect = summon_text.get_rect(
                center=(self.x, float_y - self.size - 90))
            screen.blit(summon_text, summon_rect)

            # Show vulnerability indicator
            vuln_font = pygame.font.Font(None, 18)
            vuln_text = vuln_font.render(
                "VULNERABLE! (2x DAMAGE)", True, (255, 100, 100))
            vuln_rect = vuln_text.get_rect(
                center=(self.x, float_y - self.size - 110))
            screen.blit(vuln_text, vuln_rect)
